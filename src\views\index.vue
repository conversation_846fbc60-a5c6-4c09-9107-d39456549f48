<template>
  <div class="dashboard-container" ref="dashboardRef">
    <!-- 顶部业务卡片 -->
    <el-row :gutter="screenWidth < 1600 ? 10 : 20" class="business-cards">
      <el-col :xs="6" :sm="6" :md="4" :lg="3" :xl="3" v-for="(item, index) in businessCards" :key="index">
        <div class="business-card" :class="`card-${index + 1}`">
          <div class="business-icon">
            <el-icon v-if="index === 0">
              <ShoppingBag />
            </el-icon>
            <el-icon v-else-if="index === 1">
              <User />
            </el-icon>
            <el-icon v-else-if="index === 2">
              <ShoppingCart />
            </el-icon>
            <el-icon v-else-if="index === 3">
              <Food />
            </el-icon>
            <el-icon v-else-if="index === 4">
              <SuitcaseLine />
            </el-icon>
            <el-icon v-else-if="index === 5">
              <Dessert />
            </el-icon>
            <el-icon v-else>
              <Present />
            </el-icon>
          </div>
          <div class="business-info">
            <div class="business-title">{{ item.title }}</div>
            <div class="business-value">{{ item.value }}</div>
            <div class="business-count">{{ item.count }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 中间数据统计区 -->
    <el-row :gutter="screenWidth < 1600 ? 10 : 20" class="data-charts">
      <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
        <div class="chart-card total-sales">
          <div class="sales-amount">
            <div class="amount-num">2545124.24元</div>
            <div class="amount-title">销售总额</div>
          </div>
          <div id="salesPieChart" class="pie-chart" ref="pieChartRef"></div>
          <div class="sales-legend" v-if="screenWidth >= 1366">
            <div class="legend-item" v-for="(item, index) in businessCardLegend" :key="index">
              <span class="legend-color" :class="`color-${index + 1}`"></span>
              <span class="legend-text">业务{{ index + 1 }}</span>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
        <div class="chart-card trade-trend">
          <div class="trend-title">近7天交易趋势</div>
          <div id="tradeTrendChart" class="trend-chart" ref="trendChartRef"></div>
          <div class="trend-legend" v-if="screenWidth >= 1366">
            <div class="legend-item">
              <span class="legend-color color-blue"></span>
              <span class="legend-text">销售</span>
            </div>
            <div class="legend-item">
              <span class="legend-color color-cyan"></span>
              <span class="legend-text">充值</span>
            </div>
            <div class="legend-item">
              <span class="legend-color color-green"></span>
              <span class="legend-text">退款</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 底部数据分析区 -->
    <el-row :gutter="screenWidth < 1600 ? 10 : 20" class="data-analysis">
      <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
        <div class="analysis-card">
          <div class="member-data">
            <div class="total-members">
              <div class="member-count">{{ totalMembers }}<span>人</span></div>
              <div class="member-title">会员总数</div>
            </div>
            <div class="member-stats">
              <div class="stat-item">
                <div class="stat-label">下单会员总数（近7天）</div>
                <div class="stat-value">4541<span>人</span></div>
                <div class="stat-progress">
                  <div class="progress-bar" style="width: 36%"></div>
                </div>
                <div class="stat-percent">36%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">充值会员总数（近7天）</div>
                <div class="stat-value">1248<span>人</span></div>
                <div class="stat-progress">
                  <div class="progress-bar" style="width: 16%"></div>
                </div>
                <div class="stat-percent">16%</div>
              </div>
            </div>
          </div>

          <div class="new-members">
            <div class="subtitle">
              新增会员
              <div class="time-filter">
                <el-radio-group v-model="newMemberTimeFilter" size="small" @change="handleNewMemberTimeFilterChange">
                  <el-radio-button label="daily">当天</el-radio-button>
                  <el-radio-button label="weekly">当周</el-radio-button>
                  <el-radio-button label="monthly">当月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div id="newMemberChart" class="member-chart" ref="memberChartRef"></div>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
        <div class="analysis-card">
          <div class="data-module account-data">
            <div class="account-summary">
              <div class="account-item">
                <div class="account-icon">
                  <el-icon>
                    <Money />
                  </el-icon>
                </div>
                <div class="account-info">
                  <div class="account-value">124800元</div>
                  <div class="account-title">充值总额</div>
                </div>
              </div>
              <div class="account-item">
                <div class="account-icon refund-icon">
                  <el-icon>
                    <Operation />
                  </el-icon>
                </div>
                <div class="account-info">
                  <div class="account-value">42152.24元</div>
                  <div class="account-title">退款总额</div>
                </div>
              </div>
            </div>
          </div>

          <div class="data-module store-data">
            <div class="store-stat-header">
              <div class="stat-item">
                <div class="stat-title">商品总数</div>
                <div class="stat-value">{{ productCount }}<span>个</span></div>
                <div class="stat-line"></div>
              </div>
              <div class="stat-item">
                <div class="stat-title">商家</div>
                <div class="stat-value">{{ sellerCount }}<span>个</span></div>
                <div class="stat-line"></div>
              </div>
            </div>

            <div class="active-users-stat">
              <div class="active-title">活跃用户数据</div>
              <div class="active-data-row">
                <div class="active-item">
                  <div class="active-label">每日活跃</div>
                  <div class="active-value">{{ activeUsers.daily }}<span>人</span></div>
                </div>
                <div class="active-item">
                  <div class="active-label">每周活跃</div>
                  <div class="active-value">{{ activeUsers.weekly }}<span>人</span></div>
                </div>
                <div class="active-item">
                  <div class="active-label">每月活跃</div>
                  <div class="active-value">{{ activeUsers.monthly }}<span>人</span></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 店铺销售额排行榜和收藏量前三商品 -->
    <el-row :gutter="screenWidth < 1600 ? 10 : 20" class="bottom-row">
      <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
        <div class="store-rank-card">
          <div class="rank-title">店铺销售额排行榜</div>
          <div class="rank-list">
            <div class="rank-item" v-for="i in (screenWidth < 1366 ? 8 : 10)" :key="i">
              <div class="rank-num">{{ i }}</div>
              <div class="store-title">店铺名称店铺名称店铺名称店铺名称</div>
              <div class="store-amount">12400元</div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
        <div class="top-collected-products">
          <div class="collected-title">收藏量前三商品</div>
          <div class="products-row">
            <div class="product-item" v-for="(item, index) in topCollectedProducts" :key="index">
              <div class="product-image">
                <el-image :src="item.mainImage" fit="cover"></el-image>
              </div>
              <div class="product-name">{{ item.name }}</div>
              <div class="collect-count">收藏量: {{ item.collectCount }}</div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Index">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import request from '@/utils/request'

// 屏幕尺寸响应式变量
const screenWidth = ref(window.innerWidth)
const dashboardRef = ref(null)
const pieChartRef = ref(null)
const trendChartRef = ref(null)
const memberChartRef = ref(null)

// 图表实例
let pieChart = null
let trendChart = null
let memberChart = null

// 新增会员数据
const newMemberData = ref({
  dates: [],
  counts: []
})

// 商家数量
const sellerCount = ref(0)

// 商品总数
const productCount = ref(0)

// 活跃用户数
const activeUsers = ref({
  daily: 0,
  weekly: 0,
  monthly: 0
})

// 收藏量前三商品
const topCollectedProducts = ref([])

// 新增会员时间筛选
const newMemberTimeFilter = ref('weekly') // 默认显示当周数据

// 会员总数
const totalMembers = ref(0)

// 获取会员总数
const fetchTotalMembers = async () => {
  try {
    const res = await request({
      url: '/system/users/countUserAll',
      method: 'get'
    })

    if (res.code === 200) {
      totalMembers.value = res.data
    }
  } catch (error) {
    console.error('获取会员总数失败:', error)
  }
}

// 获取最近7天新增用户数据
const fetchNewUserData = async () => {
  try {
    const res = await request({
      url: '/system/users/countWeeklyNewUsers',
      method: 'get'
    })

    if (res.code === 200 && res.data) {
      // 处理数据
      const sortedData = [...res.data].sort((a, b) => new Date(a.date) - new Date(b.date))
      newMemberData.value.dates = sortedData.map(item => item.date.substring(5)) // 只取月-日部分
      newMemberData.value.counts = sortedData.map(item => parseInt(item.count))

      // 更新图表
      if (memberChart) {
        updateNewMemberChart()
      }
    }
  } catch (error) {
    console.error('获取新增用户数据失败:', error)
  }
}

// 获取当天新增用户数据
const fetchDailyNewUserData = async () => {
  try {
    const res = await request({
      url: '/system/users/countDailyNewUsers',
      method: 'get'
    })

    if (res.code === 200 && res.data) {
      // 处理数据
      const sortedData = [...res.data].sort((a, b) => new Date(a.date) - new Date(b.date))
      newMemberData.value.dates = sortedData.map(item => item.date.substring(5)) // 只取月-日部分
      newMemberData.value.counts = sortedData.map(item => parseInt(item.count))

      // 更新图表
      if (memberChart) {
        updateNewMemberChart()
      }
    }
  } catch (error) {
    console.error('获取当天新增用户数据失败:', error)
  }
}

// 获取当月新增用户数据
const fetchMonthlyNewUserData = async () => {
  try {
    const res = await request({
      url: '/system/users/countMonthlyNewUsers',
      method: 'get'
    })

    if (res.code === 200 && res.data) {
      // 处理数据
      const sortedData = [...res.data].sort((a, b) => new Date(a.date) - new Date(b.date))
      newMemberData.value.dates = sortedData.map(item => item.date.substring(5)) // 只取月-日部分
      newMemberData.value.counts = sortedData.map(item => parseInt(item.count))

      // 更新图表
      if (memberChart) {
        updateNewMemberChart()
      }
    }
  } catch (error) {
    console.error('获取当月新增用户数据失败:', error)
  }
}

// 处理新增会员时间筛选变化
const handleNewMemberTimeFilterChange = (value) => {
  switch (value) {
    case 'daily':
      fetchDailyNewUserData()
      break
    case 'weekly':
      fetchNewUserData()
      break
    case 'monthly':
      fetchMonthlyNewUserData()
      break
  }
}

// 获取商家数量
const fetchSellerCount = async () => {
  try {
    const res = await request({
      url: '/system/sellers/countStores',
      method: 'get'
    })

    if (res.code === 200) {
      sellerCount.value = res.data
    }
  } catch (error) {
    console.error('获取商家数量失败:', error)
  }
}

// 获取商品总数
const fetchProductCount = async () => {
  try {
    const res = await request({
      url: '/system/products/countProducts',
      method: 'get'
    })

    if (res.code === 200) {
      productCount.value = res.data
    }
  } catch (error) {
    console.error('获取商品总数失败:', error)
  }
}

// 获取活跃用户数据
const fetchActiveUsers = async () => {
  try {
    // 获取每日活跃用户
    const dailyRes = await request({
      url: '/system/users/countActiveUsersDaily',
      method: 'get'
    })

    if (dailyRes.code === 200) {
      activeUsers.value.daily = dailyRes.data

    }

    // 获取每周活跃用户
    const weeklyRes = await request({
      url: '/system/users/countActiveUsersWeekly',
      method: 'get'
    })

    if (weeklyRes.code === 200) {
      activeUsers.value.weekly = weeklyRes.data
    }

    // 获取每月活跃用户
    const monthlyRes = await request({
      url: '/system/users/countActiveUsersMonthly',
      method: 'get'
    })

    if (monthlyRes.code === 200) {
      activeUsers.value.monthly = monthlyRes.data
    }
  } catch (error) {
    console.error('获取活跃用户数据失败:', error)
  }
}

// 获取收藏量前三商品
const fetchTopCollectedProducts = async () => {
  try {
    const res = await request({
      url: '/system/collect/getMostCollectedProducts',
      method: 'get'
    })

    if (res.code === 200 && res.data) {
      topCollectedProducts.value = res.data
    }
  } catch (error) {
    console.error('获取收藏量前三商品失败:', error)
  }
}

// 业务卡片数据
const businessCards = ref([
  { title: '业务1', value: '1324.25元', count: '5笔' },
  { title: '业务2', value: '1324.25元', count: '5笔' },
  { title: '业务3', value: '1324.25元', count: '5笔' },
  { title: '业务4', value: '1324.25元', count: '5笔' },
  { title: '业务5', value: '1324.25元', count: '5笔' },
  { title: '业务6', value: '1324.25元', count: '5笔' },
  { title: '业务7', value: '1324.25元', count: '5笔' },
  { title: '业务8', value: '1324.25元', count: '5笔' }
])


// 饼图图例数据
const businessCardLegend = ref([
  { name: '业务1', value: '17.1%' },
  { name: '业务2', value: '17.1%' },
  { name: '业务3', value: '19.74%' },
  { name: '业务4', value: '16.45%' },
  { name: '业务5', value: '13.16%' },
  { name: '业务6', value: '9.87%' },
  { name: '业务7', value: '6.58%' }
])

// 监听窗口大小变化
const handleResize = () => {
  screenWidth.value = window.innerWidth
  resizeAllCharts()
}

// 调整所有图表大小
const resizeAllCharts = () => {
  nextTick(() => {
    if (pieChart) pieChart.resize()
    if (trendChart) trendChart.resize()
    if (memberChart) memberChart.resize()
  })
}

// 初始化销售饼图
const initSalesPieChart = () => {
  if (pieChartRef.value) {
    pieChart = echarts.init(pieChartRef.value)
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      series: [
        {
          type: 'pie',
          radius: screenWidth.value < 1366 ? ['65%', '85%'] : ['60%', '85%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 5,
            borderColor: '#ffffff',
            borderWidth: 2
          },
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: false
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: 17.1, name: '业务1', itemStyle: { color: '#409EFF' } },
            { value: 17.1, name: '业务2', itemStyle: { color: '#67C23A' } },
            { value: 19.74, name: '业务3', itemStyle: { color: '#E6A23C' } },
            { value: 16.45, name: '业务4', itemStyle: { color: '#F56C6C' } },
            { value: 13.16, name: '业务5', itemStyle: { color: '#909399' } },
            { value: 9.87, name: '业务6', itemStyle: { color: '#79BBFF' } },
            { value: 6.58, name: '业务7', itemStyle: { color: '#FCB875' } }
          ]
        }
      ]
    }
    option && pieChart.setOption(option)
  }
}

// 初始化交易趋势图
const initTradeTrendChart = () => {
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
    const option = {
      grid: {
        top: 20,
        bottom: 30,
        left: 40,
        right: 40
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['03-09', '03-10', '03-11', '03-12', '03-13', '03-14', '03-15'],
        axisLine: {
          lineStyle: {
            color: '#dcdfe6'
          }
        },
        axisLabel: {
          color: '#606266',
          fontSize: screenWidth.value < 1366 ? 10 : 12
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisLabel: {
          color: '#606266',
          fontSize: screenWidth.value < 1366 ? 10 : 12
        },
        splitLine: {
          lineStyle: {
            color: '#ebeef5'
          }
        }
      },
      series: [
        {
          name: '销售',
          type: 'bar',
          data: [18, 20, 30, 40, 50, 60, 60],
          itemStyle: {
            color: '#409EFF'
          }
        },
        {
          name: '充值',
          type: 'bar',
          data: [20, 25, 50, 40, 30, 100, 120],
          itemStyle: {
            color: '#67C23A'
          }
        },
        {
          name: '退款',
          type: 'bar',
          data: [15, 20, 45, 55, 40, 75, 65],
          itemStyle: {
            color: '#79BBFF'
          }
        }
      ]
    }
    option && trendChart.setOption(option)
  }
}

// 初始化新增会员图表
const initNewMemberChart = () => {
  if (memberChartRef.value) {
    memberChart = echarts.init(memberChartRef.value)
    updateNewMemberChart()
  }
}

// 更新新增会员图表
const updateNewMemberChart = () => {
  const dates = newMemberData.value.dates.length > 0 ?
    newMemberData.value.dates :
    [new Date().toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }).replace(/\//g, '-')]

  const counts = newMemberData.value.counts.length > 0 ?
    newMemberData.value.counts :
    [0]

  // 根据时间筛选选择不同的图表类型
  const chartType = newMemberTimeFilter.value === 'daily' ? 'bar' : 'line'

  const option = {
    grid: {
      top: 20,
      bottom: 30,
      left: 40,
      right: 40
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: {
          color: '#dcdfe6'
        }
      },
      axisLabel: {
        color: '#606266',
        fontSize: screenWidth.value < 1366 ? 10 : 12
      }
    },
    yAxis: {
      type: 'value',
      minInterval: 1, // 最小间隔为1，确保只显示整数
      axisLine: {
        show: false
      },
      axisLabel: {
        color: '#606266',
        fontSize: screenWidth.value < 1366 ? 10 : 12,
        formatter: '{value}' // 确保显示整数
      },
      splitLine: {
        lineStyle: {
          color: '#ebeef5'
        }
      }
    },
    series: [
      {
        name: '新增会员',
        type: chartType,
        data: counts,
        smooth: chartType === 'line',
        symbolSize: chartType === 'line' ? (screenWidth.value < 1366 ? 6 : 8) : undefined,
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: chartType === 'line' ? {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(64, 158, 255, 0.5)'
              },
              {
                offset: 1,
                color: 'rgba(64, 158, 255, 0.1)'
              }
            ]
          }
        } : undefined
      }
    ]
  }
  memberChart && memberChart.setOption(option)
}

// 初始化所有图表
const initAllCharts = () => {
  initSalesPieChart()
  initTradeTrendChart()
  initNewMemberChart()
}

onMounted(() => {
  // 获取会员总数
  fetchTotalMembers()

  // 获取新增用户数据
  fetchNewUserData()

  // 获取商家数量
  fetchSellerCount()

  // 获取商品总数
  fetchProductCount()

  // 获取活跃用户数据
  fetchActiveUsers()

  // 获取收藏量前三商品
  fetchTopCollectedProducts()

  // 初始化图表
  nextTick(() => {
    initAllCharts()
  })

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 清除事件监听
  window.removeEventListener('resize', handleResize)

  // 销毁图表实例
  if (pieChart) pieChart.dispose()
  if (trendChart) trendChart.dispose()
  if (memberChart) memberChart.dispose()
})
</script>

<style scoped lang="scss">
.dashboard-container {
  background-color: #f5f5f5;
  color: #333;
  padding: 20px;
  min-height: calc(100vh - 84px);
  overflow-x: hidden;
  /* 设置页面最小宽度，避免页面过小时布局错乱 */
  min-width: 1200px;
  /* 增加底部间距，使页面更长 */
  padding-bottom: 40px;
}

/* 顶部业务卡片样式 */
.business-cards {
  margin-bottom: 20px;
}

.business-card {
  height: 100px;
  border-radius: 4px;
  background-color: #ffffff;
  border: 1px solid #ebeef5;
  padding: 15px;
  box-sizing: border-box;
  display: flex;
  transition: all 0.3s;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .business-icon {
    background-color: rgba(64, 158, 255, 0.1);
    width: 40px;
    height: 40px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
  }

  .business-info {
    display: flex;
    flex-direction: column;
    flex: 1;
  }

  .business-title {
    font-size: 14px;
    color: #909399;
    margin-bottom: 5px;
  }

  .business-value {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 3px;
    color: #303133;
  }

  .business-count {
    font-size: 12px;
    color: #909399;
  }

  &.card-1 .business-icon {
    background-color: rgba(64, 158, 255, 0.1);
    color: #409EFF;
  }

  &.card-2 .business-icon {
    background-color: rgba(103, 194, 58, 0.1);
    color: #67C23A;
  }

  &.card-3 .business-icon {
    background-color: rgba(230, 162, 60, 0.1);
    color: #E6A23C;
  }

  &.card-4 .business-icon {
    background-color: rgba(245, 108, 108, 0.1);
    color: #F56C6C;
  }

  &.card-5 .business-icon {
    background-color: rgba(144, 147, 153, 0.1);
    color: #909399;
  }

  &.card-6 .business-icon {
    background-color: rgba(121, 187, 255, 0.1);
    color: #79BBFF;
  }

  &.card-7 .business-icon {
    background-color: rgba(252, 184, 117, 0.1);
    color: #FCB875;
  }
}

// 图表卡片样式
.data-charts {
  margin-bottom: 20px;

  .chart-card {
    background-color: #ffffff;
    border-radius: 4px;
    padding: 20px;
    height: 400px;
    /* 增加图表高度 */
    position: relative;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    /* 响应式高度调整 */
    @media screen and (max-width: 1600px) {
      height: 380px;
      /* 增加图表高度 */
      padding: 15px;
    }

    @media screen and (max-width: 1366px) {
      height: 340px;
      /* 增加图表高度 */
      padding: 12px;
    }
  }

  // 销售总额样式
  .total-sales {
    display: flex;
    flex-direction: column;

    .sales-amount {
      text-align: center;
      margin-bottom: 10px;

      .amount-num {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #303133;
      }

      .amount-title {
        font-size: 14px;
        color: #909399;
      }
    }

    .pie-chart {
      height: 270px;
      /* 增加饼图高度 */
      margin: 10px 0;
    }

    .sales-legend {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;

      .legend-item {
        display: flex;
        align-items: center;
        margin: 0 10px;

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
          margin-right: 5px;

          &.color-1 {
            background-color: #409EFF;
          }

          &.color-2 {
            background-color: #67C23A;
          }

          &.color-3 {
            background-color: #E6A23C;
          }

          &.color-4 {
            background-color: #F56C6C;
          }

          &.color-5 {
            background-color: #909399;
          }

          &.color-6 {
            background-color: #79BBFF;
          }

          &.color-7 {
            background-color: #FCB875;
          }
        }

        .legend-text {
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }

  // 交易趋势样式
  .trade-trend {
    .trend-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 15px;
      color: #303133;
    }

    .trend-chart {
      height: 305px;
      /* 增加趋势图高度 */
    }

    .trend-legend {
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;

      .legend-item {
        display: flex;
        align-items: center;
        margin-left: 15px;

        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 2px;
          margin-right: 5px;

          &.color-blue {
            background-color: #409EFF;
          }

          &.color-cyan {
            background-color: #67C23A;
          }

          &.color-green {
            background-color: #79BBFF;
          }
        }

        .legend-text {
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }
}

// 数据分析区域样式
.data-analysis {
  .analysis-card {
    background-color: #ffffff;
    border-radius: 4px;
    height: 400px;
    /* 增加分析卡片高度 */
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;

    /* 响应式高度调整 */
    @media screen and (max-width: 1600px) {
      height: 380px;
      /* 增加分析卡片高度 */
    }

    @media screen and (max-width: 1366px) {
      height: 340px;
      /* 增加分析卡片高度 */
    }
  }

  // 会员数据样式
  .member-data {
    padding: 20px;
    display: flex;
    border-bottom: 1px solid #ebeef5;
    background-color: #ffffff;
    border-radius: 4px 4px 0 0;

    .total-members {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .member-count {
        font-size: 32px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #303133;

        span {
          font-size: 14px;
          font-weight: normal;
          color: #909399;
        }
      }

      .member-title {
        font-size: 14px;
        color: #909399;
      }
    }

    .member-stats {
      flex: 2;

      .stat-item {
        margin-bottom: 15px;

        .stat-label {
          font-size: 12px;
          color: #909399;
          margin-bottom: 5px;
        }

        .stat-value {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 5px;
          color: #303133;

          span {
            font-size: 12px;
            font-weight: normal;
            color: #909399;
          }
        }

        .stat-progress {
          height: 4px;
          background-color: #e9e9e9;
          border-radius: 2px;
          margin-bottom: 3px;
          position: relative;

          .progress-bar {
            position: absolute;
            height: 100%;
            background-color: #409EFF;
            border-radius: 2px;
          }
        }

        .stat-percent {
          font-size: 12px;
          color: #409EFF;
          text-align: right;
        }
      }
    }
  }

  .new-members {
    padding: 15px 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border-radius: 0 0 4px 4px;

    .subtitle {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 10px;
      color: #303133;
      padding-bottom: 5px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .time-filter {
        .el-radio-group {
          .el-radio-button__inner {
            padding: 5px 10px;
            font-size: 12px;
          }
        }
      }
    }

    .member-chart {
      flex: 1;
      height: 240px;
    }
  }

  // 账户数据样式
  .account-data {
    margin-bottom: 15px;

    .account-summary {
      display: flex;
      background-color: #ffffff;
      border: 1px solid #ebeef5;
      border-radius: 4px;

      .account-item {
        flex: 1;
        padding: 25px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        .account-icon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background-color: rgba(64, 158, 255, 0.1);
          color: #409EFF;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          margin-bottom: 10px;

          &.refund-icon {
            background-color: rgba(245, 108, 108, 0.1);
            color: #F56C6C;
          }
        }

        .account-info {
          .account-value {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
            color: #409EFF;
          }

          .account-title {
            font-size: 14px;
            color: #909399;
          }
        }

        &:first-child {
          position: relative;

          &:after {
            content: '';
            position: absolute;
            right: 0;
            top: 25%;
            height: 50%;
            width: 1px;
            background-color: #ebeef5;
          }
        }
      }
    }
  }

  // 店铺数据样式
  .store-data {
    padding: 15px 20px;
    flex: 1;
    display: flex;
    flex-direction: column;

    .store-stat-header {
      display: flex;
      margin-bottom: 15px;
      height: 70px;
      background-color: #ffffff;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 0 10px;

      .stat-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;

        .stat-title {
          font-size: 14px;
          color: #909399;
          margin-bottom: 5px;
        }

        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #409EFF;

          span {
            font-size: 14px;
            font-weight: normal;
            color: #909399;
          }
        }

        .stat-line {
          width: 30%;
          height: 2px;
          background: linear-gradient(90deg, transparent, #409EFF, transparent);
          position: absolute;
          bottom: -5px;
        }
      }
    }
  }

  // 活跃用户数据样式
  .active-users-stat {
    margin-top: -15px;
    background-color: #ffffff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 15px;
    /* 增加内边距 */
    margin-bottom: 15px;
    /* 增加底部间距 */

    .active-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 15px;
      /* 增加标题底部间距 */
      padding-bottom: 5px;
      border-bottom: 1px solid #ebeef5;
    }

    .active-data-row {
      display: flex;
      justify-content: space-around;
      padding: 10px 0;
      /* 增加上下内边距 */

      .active-item {
        text-align: center;

        .active-label {
          font-size: 12px;
          color: #909399;
          margin-bottom: 10px;
          /* 增加标签底部间距 */
        }

        .active-value {
          font-size: 18px;
          /* 增加数值字体大小 */
          font-weight: 600;
          color: #409EFF;

          span {
            font-size: 12px;
            font-weight: normal;
            color: #909399;
            margin-left: 2px;
          }
        }
      }
    }
  }
}

// 底部行样式
.bottom-row {
  margin-top: 20px;
  margin-bottom: 20px;
}

// 店铺销售额排行榜样式
.store-rank-card {
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;

  .rank-title {
    font-size: 16px;
    font-weight: 600;
    padding: 15px 20px;
    color: #303133;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
  }

  .rank-list {
    padding: 15px 0;
    display: flex;
    flex-wrap: wrap;
    max-height: 400px;
    overflow-y: auto;

    .rank-item {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      width: 50%;
      box-sizing: border-box;

      @media screen and (max-width: 1600px) {
        width: 50%;
      }

      @media screen and (max-width: 1366px) {
        width: 50%;
      }

      .rank-num {
        width: 25px;
        height: 25px;
        background-color: #f5f7fa;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        margin-right: 10px;
        color: #606266;
      }

      &:nth-child(-n+3) .rank-num {
        color: #ffffff;
      }

      &:nth-child(1) .rank-num {
        background-color: #f56c6c;
      }

      &:nth-child(2) .rank-num {
        background-color: #e6a23c;
      }

      &:nth-child(3) .rank-num {
        background-color: #409eff;
      }

      .store-title {
        flex: 1;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 10px;
        color: #303133;
      }

      .store-amount {
        font-size: 14px;
        font-weight: 600;
        width: 85px;
        text-align: right;
        color: #409EFF;
      }
    }
  }
}

// 收藏量前三商品样式
.top-collected-products {
  background-color: #ffffff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  height: 100%;

  .collected-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ebeef5;
  }

  .products-row {
    display: flex;
    justify-content: space-around;
    padding: 10px 0;

    .product-item {
      text-align: center;
      width: 30%;

      .product-image {
        width: 90px;
        height: 90px;
        margin: 0 auto 10px;
        border-radius: 4px;
        overflow: hidden;
        border: 1px solid #ebeef5;

        .el-image {
          width: 100%;
          height: 100%;
        }
      }

      .product-name {
        font-size: 12px;
        color: #303133;
        margin-bottom: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .collect-count {
        font-size: 12px;
        color: #409EFF;
        font-weight: 600;
        padding: 5px 0;
      }
    }
  }
}

/* 添加统一的响应式文字大小调整 */
@media screen and (max-width: 1600px) {
  .amount-num {
    font-size: 24px !important;
  }

  .member-count {
    font-size: 28px !important;
  }

  .subtitle,
  .trend-title {
    font-size: 14px !important;
  }

  .stat-value,
  .grid-value {
    font-size: 14px !important;
  }

  .store-name,
  .store-sales {
    font-size: 12px !important;
  }
}

@media screen and (max-width: 1366px) {
  .amount-num {
    font-size: 20px !important;
  }

  .member-count {
    font-size: 24px !important;
  }

  .stat-label,
  .legend-text {
    font-size: 11px !important;
  }
}
</style>