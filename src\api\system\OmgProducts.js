import request from '@/utils/request'

// 查询omg_商品列表
export function listOmgProducts(query) {
  return request({
    url: '/system/OmgProducts/list',
    method: 'get',
    params: query,
    timeout: 20000 // 20秒超时
  })
}

// 查询omg_商品详细
export function getOmgProducts(productId) {
  return request({
    url: '/system/OmgProducts/' + productId,
    method: 'get'
  })
}

// 新增omg_商品
export function addOmgProducts(data) {
  return request({
    url: '/system/OmgProducts',
    method: 'post',
    data: data
  })
}

// 修改商品状态
export function updateProductStatus(productId, status) {
  return request({
    url: `/system/OmgProducts/status`,
    method: 'put',
    data: { productId, status }
  });
}

// 修改omg_商品
export function updateOmgProducts(data) {
  return request({
    url: '/system/OmgProducts',
    method: 'put',
    data: data
  })
}

// 删除omg_商品
export function delOmgProducts(productId) {
  return request({
    url: '/system/OmgProducts/' + productId,
    method: 'delete'
  })
}

// 批量导入omg_商品
export function importOmgProducts(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request({
    url: '/system/OmgProducts/importData',
    method: 'post',
    data: formData,
    headers: { 'Content-Type': 'multipart/form-data' }
  });
}

// 批量更新所有商品QC
export function batchUpdateAllQC() {
  return request({
    url: '/front/omg/products/qc-update/batch-all',
    method: 'post',
    timeout: 60000 // 60秒超时，因为批量更新可能需要较长时间
  });
}

// 全量同步重量/运输天数
export function syncAllProductsWeightAndShipping() {
  return request({
    url: '/system/cnfans-sync/products/all',
    method: 'post',
    timeout: 120000 // 120秒超时，全量同步可能需要更长时间
  });
}

// 更新所有商品主图库
export function updateAllProductMainImages() {
  return request({
    url: '/system/OmgProducts/updateAllProductMainImages',
    method: 'post',
    timeout: 180000 // 180秒超时，更新主图库可能需要更长时间
  });
}

// 根据SKU获取商品所有主图
export function getProductMainImagesBySku(sku) {
  return request({
    url: `/system/OmgProducts/updateMainImagesBySku/${sku}`,
    method: 'post',
    timeout: 30000 // 30秒超时
  });
}